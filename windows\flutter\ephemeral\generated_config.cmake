# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\scoop\\persist\\fvm\\versions\\3.22.1" FLUTTER_ROOT)
file(TO_CMAKE_PATH "E:\\Work\\SUS" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\scoop\\persist\\fvm\\versions\\3.22.1"
  "PROJECT_DIR=E:\\Work\\SUS"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\scoop\\persist\\fvm\\versions\\3.22.1"
  "FLUTTER_EPHEMERAL_DIR=E:\\Work\\SUS\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=E:\\Work\\SUS"
  "FLUTTER_TARGET=E:\\Work\\SUS\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC81NWVhZTY4NjRiMjk2ZGQ5ZjQzYjJjYzc1NzdlYzI1NmU1YzMyYThkLw=="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=E:\\Work\\SUS\\.dart_tool\\package_config.json"
)
